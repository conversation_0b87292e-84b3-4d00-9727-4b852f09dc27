# TabNest Marketing Website

A professional marketing website for TabNest, a browser extension that helps users manage their tabs and tab groups.

## Features

- **Responsive Design**: Works perfectly on desktop, tablet, and mobile devices
- **Modern UI/UX**: Clean, professional design with smooth animations
- **Brand Consistency**: Uses TabNest's official color palette and branding
- **Interactive Elements**: FAQ accordion, mobile navigation, smooth scrolling
- **SEO Optimized**: Proper meta tags and semantic HTML structure
- **Accessibility**: Focus states, ARIA labels, and keyboard navigation support

## Brand Colors

- Primary Blue: `#5A5DD1` (Savoy Blue)
- Secondary Blue: `#3B3A8F` (Marian Blue)  
- Dark Blue: `#232181` (Resolution Blue)
- Background Light: `#F1F9FF` (Alice Blue)
- Background Lighter: `#F5FAFF` (Alice Blue variation)

## Sections

1. **Hero Section**: Clear value proposition with download CTAs
2. **Features Section**: Key functionality highlights with icons
3. **How It Works**: 3-step process explanation
4. **Benefits/Testimonials**: User benefits and social proof
5. **Download Section**: Browser-specific download options
6. **FAQ Section**: Common questions with accordion interface
7. **Footer**: Links, social media, and legal information

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # Complete CSS with responsive design
├── script.js           # Interactive JavaScript functionality
├── assets/
│   └── logo.png        # TabNest brand logo
└── README.md           # This file
```

## Development

To run the website locally:

1. **Simple HTTP Server** (Python):
   ```bash
   python -m http.server 8000
   ```
   Then visit `http://localhost:8000`

2. **Node.js HTTP Server**:
   ```bash
   npx http-server
   ```

3. **Live Server** (VS Code extension):
   - Install the Live Server extension
   - Right-click on `index.html` and select "Open with Live Server"

## Browser Support

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

## Performance Features

- Optimized CSS with CSS custom properties
- Efficient JavaScript with event delegation
- Lazy loading for animations
- Minimal external dependencies
- Compressed and optimized assets

## Accessibility Features

- Semantic HTML structure
- ARIA labels for interactive elements
- Keyboard navigation support
- Focus indicators
- Screen reader friendly
- Color contrast compliance

## SEO Features

- Semantic HTML5 structure
- Meta tags for social sharing
- Proper heading hierarchy
- Alt text for images
- Structured data ready

## Customization

### Colors
Update the CSS custom properties in `styles.css`:
```css
:root {
    --primary-blue: #5A5DD1;
    --secondary-blue: #3B3A8F;
    --dark-blue: #232181;
    /* ... */
}
```

### Content
Edit the HTML content in `index.html` to update:
- Hero messaging
- Feature descriptions
- Testimonials
- FAQ items
- Contact information

### Functionality
Modify `script.js` to:
- Add analytics tracking
- Update download URLs
- Add form handling
- Customize animations

## Deployment

The website is ready for deployment to any static hosting service:

- **Netlify**: Drag and drop the folder
- **Vercel**: Connect your Git repository
- **GitHub Pages**: Push to a GitHub repository
- **AWS S3**: Upload files to an S3 bucket
- **Firebase Hosting**: Use Firebase CLI

## Browser Extension Integration

To integrate with the actual TabNest browser extension:

1. Update download URLs in the HTML to point to actual store listings
2. Add analytics tracking for download conversions
3. Implement user feedback collection
4. Add extension usage statistics
5. Create user onboarding flows

## Future Enhancements

- [ ] Add blog section for SEO
- [ ] Implement contact form
- [ ] Add user testimonials carousel
- [ ] Create video demonstrations
- [ ] Add multi-language support
- [ ] Implement dark mode toggle
- [ ] Add extension screenshots/demos
- [ ] Create pricing page (if premium features added)

## License

This website template is created for TabNest. All rights reserved.

## Support

For questions about the website or TabNest extension:
- Email: <EMAIL>
- GitHub: [TabNest Repository]
- Discord: [TabNest Community]

---

Built with ❤️ for better tab management
