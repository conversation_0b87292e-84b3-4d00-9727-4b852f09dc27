/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Brand Colors */
  --primary-blue: #5a5dd1;
  --secondary-blue: #3b3a8f;
  --dark-blue: #232181;
  --bg-light: #f1f9ff;
  --bg-lighter: #f5faff;

  /* Additional Colors */
  --white: #ffffff;
  --text-dark: #1a1a1a;
  --text-gray: #6b7280;
  --text-light: #9ca3af;
  --border-light: #e5e7eb;
  --shadow: rgba(90, 93, 209, 0.1);
  --shadow-dark: rgba(35, 33, 129, 0.15);

  /* Typography */
  --font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;
  --font-size-4xl: 2.25rem;
  --font-size-5xl: 3rem;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 0.75rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Transitions */
  --transition: all 0.3s ease;
}

body {
  font-family: var(--font-family);
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--white);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-light);
  z-index: 1000;
  transition: var(--transition);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  text-decoration: none;
}

.nav-logo .logo {
  width: 32px;
  height: 32px;
}

.logo-text {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--primary-blue);
}

.nav-menu {
  display: flex;
  list-style: none;
  gap: var(--spacing-xl);
}

.nav-link {
  text-decoration: none;
  color: var(--text-dark);
  font-weight: 500;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-blue);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-blue);
  transition: var(--transition);
}

.nav-link:hover::after {
  width: 100%;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
}

.bar {
  width: 25px;
  height: 3px;
  background: var(--text-dark);
  margin: 3px 0;
  transition: var(--transition);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--font-size-base);
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  white-space: nowrap;
}

.btn-primary {
  background: var(--primary-blue);
  color: var(--white);
  box-shadow: 0 4px 12px var(--shadow);
}

.btn-primary:hover {
  background: var(--secondary-blue);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px var(--shadow-dark);
}

.btn-secondary {
  background: var(--white);
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-secondary:hover {
  background: var(--primary-blue);
  color: var(--white);
}

.btn-disabled {
  background: var(--text-light);
  color: var(--white);
  cursor: not-allowed;
}

/* Hero Section */
.hero {
  padding: 120px 0 80px;
  background: linear-gradient(
    135deg,
    var(--bg-lighter) 0%,
    var(--bg-light) 100%
  );
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.hero-title {
  font-size: var(--font-size-5xl);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--spacing-lg);
  color: var(--text-dark);
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-gray);
  margin-bottom: var(--spacing-2xl);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-2xl);
}

.hero-stats {
  display: flex;
  gap: var(--spacing-2xl);
}

.stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--primary-blue);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-gray);
}

/* Browser Mockup */
.browser-mockup {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: 0 20px 40px var(--shadow-dark);
  overflow: hidden;
  position: relative;
}

.browser-header {
  background: #f3f4f6;
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  border-bottom: 1px solid var(--border-light);
}

.browser-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

.browser-buttons span {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.btn-close {
  background: #ff5f57;
}
.btn-minimize {
  background: #ffbd2e;
}
.btn-maximize {
  background: #28ca42;
}

.address-bar {
  flex: 1;
  background: var(--white);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-gray);
}

.browser-content {
  padding: var(--spacing-lg);
  min-height: 300px;
  position: relative;
}

.tab-groups {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.tab-group {
  display: flex;
  gap: var(--spacing-xs);
}

.tab {
  background: var(--bg-light);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-sm);
  color: var(--text-gray);
  border: 1px solid var(--border-light);
}

.tab.active {
  background: var(--primary-blue);
  color: var(--white);
  border-color: var(--primary-blue);
}

.tabnest-popup {
  position: absolute;
  top: 50%;
  right: var(--spacing-lg);
  transform: translateY(-50%);
  background: var(--white);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  box-shadow: 0 10px 25px var(--shadow);
  min-width: 250px;
}

.popup-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-light);
}

.popup-logo {
  width: 20px;
  height: 20px;
}

.saved-sessions {
  margin-bottom: var(--spacing-lg);
}

.session {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-sm);
  background: var(--bg-lighter);
}

.session i {
  color: var(--primary-blue);
}

.session span {
  flex: 1;
  font-size: var(--font-size-sm);
}

.restore-btn {
  background: var(--primary-blue);
  color: var(--white);
  border: none;
  padding: 4px 8px;
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  cursor: pointer;
}

.save-current-btn {
  width: 100%;
  background: var(--secondary-blue);
  color: var(--white);
  border: none;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 600;
  cursor: pointer;
}

/* Section Headers */
.section-header {
  text-align: center;
  margin-bottom: var(--spacing-3xl);
}

.section-header h2 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-lg);
  color: var(--text-dark);
}

.section-header p {
  font-size: var(--font-size-xl);
  color: var(--text-gray);
  max-width: 600px;
  margin: 0 auto;
}

/* Features Section */
.features {
  padding: var(--spacing-3xl) 0;
  background: var(--white);
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: var(--spacing-2xl);
}

.feature-card {
  background: var(--white);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border-light);
  text-align: center;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
}

.feature-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-blue),
    var(--secondary-blue)
  );
  transform: scaleX(0);
  transition: var(--transition);
}

.feature-card:hover::before {
  transform: scaleX(1);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px var(--shadow);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(
    135deg,
    var(--primary-blue),
    var(--secondary-blue)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  color: var(--white);
  font-size: var(--font-size-2xl);
}

.feature-card h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-dark);
}

.feature-card p {
  color: var(--text-gray);
  line-height: 1.6;
}

/* How It Works Section */
.how-it-works {
  padding: var(--spacing-3xl) 0;
  background: var(--bg-lighter);
}

.steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
  position: relative;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
  background: var(--white);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  box-shadow: 0 4px 12px var(--shadow);
}

.step-number {
  width: 60px;
  height: 60px;
  background: var(--primary-blue);
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-xl);
  font-weight: 700;
  flex-shrink: 0;
}

.step-content h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-dark);
}

.step-content p {
  color: var(--text-gray);
  line-height: 1.6;
}

/* Benefits Section */
.benefits {
  padding: var(--spacing-3xl) 0;
  background: var(--white);
}

.benefits-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-3xl);
  align-items: center;
}

.benefits-text h2 {
  font-size: var(--font-size-4xl);
  font-weight: 700;
  margin-bottom: var(--spacing-2xl);
  color: var(--text-dark);
}

.benefit-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.benefit-item i {
  color: var(--primary-blue);
  font-size: var(--font-size-xl);
  margin-top: 4px;
}

.benefit-item h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-xs);
  color: var(--text-dark);
}

.benefit-item p {
  color: var(--text-gray);
  line-height: 1.6;
}

.testimonials {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.testimonial {
  background: var(--bg-lighter);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  border-left: 4px solid var(--primary-blue);
}

.testimonial-content p {
  font-size: var(--font-size-lg);
  font-style: italic;
  color: var(--text-dark);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

.testimonial-author strong {
  color: var(--text-dark);
  font-weight: 600;
}

.testimonial-author span {
  color: var(--text-gray);
  font-size: var(--font-size-sm);
}

/* Download Section */
.download {
  padding: var(--spacing-3xl) 0;
  background: var(--bg-light);
}

.download-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-2xl);
}

.download-card {
  background: var(--white);
  padding: var(--spacing-2xl);
  border-radius: var(--radius-xl);
  text-align: center;
  border: 1px solid var(--border-light);
  transition: var(--transition);
}

.download-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px var(--shadow);
}

.download-card.coming-soon {
  opacity: 0.6;
}

.browser-icon {
  width: 80px;
  height: 80px;
  background: var(--bg-lighter);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-lg);
  font-size: var(--font-size-2xl);
  color: var(--primary-blue);
}

.download-card h3 {
  font-size: var(--font-size-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--text-dark);
}

.download-card p {
  color: var(--text-gray);
  margin-bottom: var(--spacing-lg);
  line-height: 1.6;
}

/* FAQ Section */
.faq {
  padding: var(--spacing-3xl) 0;
  background: var(--white);
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  border: 1px solid var(--border-light);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
}

.faq-question {
  padding: var(--spacing-lg);
  background: var(--bg-lighter);
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: var(--transition);
}

.faq-question:hover {
  background: var(--bg-light);
}

.faq-question h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-dark);
}

.faq-question i {
  color: var(--primary-blue);
  transition: var(--transition);
}

.faq-item.active .faq-question i {
  transform: rotate(180deg);
}

.faq-answer {
  padding: 0 var(--spacing-lg);
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
  padding: var(--spacing-lg);
  max-height: 200px;
}

.faq-answer p {
  color: var(--text-gray);
  line-height: 1.6;
}

/* Footer */
.footer {
  background: var(--dark-blue);
  color: var(--white);
  padding: var(--spacing-3xl) 0 var(--spacing-xl);
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: var(--spacing-2xl);
  margin-bottom: var(--spacing-2xl);
}

.footer-section h4 {
  font-size: var(--font-size-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  color: var(--white);
}

.footer-section ul {
  list-style: none;
}

.footer-section ul li {
  margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition);
}

.footer-section ul li a:hover {
  color: var(--white);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
}

.footer-logo .logo {
  width: 32px;
  height: 32px;
}

.footer-section p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.social-links {
  display: flex;
  gap: var(--spacing-md);
}

.social-links a {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white);
  text-decoration: none;
  transition: var(--transition);
}

.social-links a:hover {
  background: var(--primary-blue);
  transform: translateY(-2px);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--spacing-lg);
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

.footer-bottom p {
  margin-bottom: var(--spacing-xs);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
    text-align: center;
  }

  .benefits-content {
    grid-template-columns: 1fr;
    gap: var(--spacing-2xl);
  }

  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
  }
}

@media (max-width: 768px) {
  .nav-menu {
    position: fixed;
    left: -100%;
    top: 70px;
    flex-direction: column;
    background-color: var(--white);
    width: 100%;
    text-align: center;
    transition: 0.3s;
    box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
    padding: var(--spacing-lg) 0;
  }

  .nav-menu.active {
    left: 0;
  }

  .nav-menu li {
    margin: var(--spacing-sm) 0;
  }

  .nav-toggle {
    display: flex;
  }

  .nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
  }

  .nav-toggle.active .bar:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
  }

  .nav-toggle.active .bar:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
  }

  .hero {
    padding: 100px 0 60px;
    min-height: auto;
  }

  .hero-title {
    font-size: var(--font-size-4xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    justify-content: center;
  }

  .section-header h2 {
    font-size: var(--font-size-3xl);
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .steps {
    grid-template-columns: 1fr;
  }

  .step {
    flex-direction: column;
    text-align: center;
  }

  .download-options {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--spacing-md);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-base);
  }

  .section-header h2 {
    font-size: var(--font-size-2xl);
  }

  .feature-card,
  .step,
  .download-card {
    padding: var(--spacing-lg);
  }

  .browser-mockup {
    margin: 0 -var(--spacing-md);
  }

  .tabnest-popup {
    position: static;
    transform: none;
    margin-top: var(--spacing-lg);
  }
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.btn:focus,
.nav-link:focus,
.faq-question:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Loading animation for buttons */
.btn:active {
  transform: scale(0.98);
}
